// Producer for PGMQ, sends messages to the queue for benchmarking
const { Pgmq } = require('pgmq-js');
const { Client } = require('pg');
const fs = require('fs').promises; // Use async fs operations
const fsSync = require('fs'); // Keep sync for initial setup only
const path = require('path');

function logWithTimestamp(message) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  const workerId = process.env.pm_id || process.pid;
  console.log(`[${timestamp}] [Worker-${workerId}] ${message}`);
}

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433', 10),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: false,
};

// Function to generate payload of specified size in KB
function generatePayload(sizeInKB) {
  const baseChar = 'x';
  const bytesPerChar = 1; // Approximation for ASCII
  const totalChars = sizeInKB * 1024 / bytesPerChar;
  return baseChar.repeat(totalChars);
}

// Optimized CSV setup with minimal file system operations
function setupCsvFile() {
  const metricsDir = path.join(__dirname, 'metrics');

  // Use synchronous operations only for initial setup
  if (!fsSync.existsSync(metricsDir)) {
    fsSync.mkdirSync(metricsDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/:/g, '-').replace('T', '_').split('.')[0];

  // Simple header creation - let each worker create its own file to avoid locking
  const workerId = process.env.pm_id || process.pid;
  const workerFileName = `producer_metrics_${timestamp}_worker_${workerId}.csv`;
  const workerFilePath = path.join(metricsDir, workerFileName);

  if (!fsSync.existsSync(workerFilePath)) {
    fsSync.writeFileSync(workerFilePath, 'message_id,size_kb,sent_timestamp,worker_id\n');
  }

  return workerFilePath;
}

// High-performance async CSV writing with minimal locking overhead
async function appendToCsv(filePath, data) {
  try {
    // Use async file operations to avoid blocking the event loop
    await fs.appendFile(filePath, data);
  } catch (error) {
    // Simple fallback without complex retry logic
    logWithTimestamp(`CSV write error: ${error.message}`);
    // Try once more synchronously as fallback
    try {
      fsSync.appendFileSync(filePath, data);
    } catch (fallbackError) {
      logWithTimestamp(`CSV fallback write failed: ${fallbackError.message}`);
    }
  }
}

// Batch message sending for better performance
async function sendMessageBatch(pgmq, queueName, messages) {
  const promises = messages.map(message => pgmq.msg.send(queueName, message));
  return Promise.all(promises);
}

async function setupPgmq() {
  logWithTimestamp('Menyiapkan ekstensi PGMQ...');
  const client = new Client(dbConfig);
  try {
    await client.connect();
    await client.query('CREATE EXTENSION IF NOT EXISTS pgmq');
    await client.query('CREATE SCHEMA IF NOT EXISTS pgmq');
  } catch (error) {
    logWithTimestamp(`Error saat menyiapkan PGMQ: ${error.message}`);
    throw error;
  } finally {
    await client.end();
  }
}

async function runProducer() {
  await setupPgmq();
  const queueName = process.env.QUEUE_NAME || 'pgmq_test';
  const totalMessages = parseInt(process.env.TOTAL_MESSAGES || '10000', 10);
  const messageSizeKB = parseInt(process.env.MESSAGE_SIZE_KB || '1', 10);
  const workerId = process.env.pm_id || process.pid;

  // Calculate messages per worker in cluster mode
  const workerCount = parseInt(process.env.instances || '1', 10);
  const workerIndex = parseInt(process.env.NODE_APP_INSTANCE || '0', 10);
  const messagesPerWorker = Math.floor(totalMessages / workerCount);
  const remainderMessages = totalMessages % workerCount;

  // Distribute messages: some workers get one extra message
  const workerMessages = messagesPerWorker + (workerIndex < remainderMessages ? 1 : 0);
  const startMessageId = workerIndex * messagesPerWorker + Math.min(workerIndex, remainderMessages);

  // Generate payload of specified size
  const payload = generatePayload(messageSizeKB);
  logWithTimestamp(`Ukuran pesan: ${messageSizeKB} KB`);
  logWithTimestamp(`Worker akan mengirim ${workerMessages} pesan (ID ${startMessageId} - ${startMessageId + workerMessages - 1})`);

  // Setup CSV file (shared across all workers)
  const csvFilePath = setupCsvFile();
  logWithTimestamp(`Menyimpan data ke: ${csvFilePath}`);

  let sent = 0;
  let pgmq;
  const bufferSize = 500; // Increased buffer size for better performance

  // Pre-allocate string builder for better performance
  let csvData = '';

  try {
    logWithTimestamp('Menghubungkan ke PostgreSQL...');
    pgmq = await Pgmq.new(dbConfig);

    // Create queue (only one worker needs to do this, but it's idempotent)
    await pgmq.queue.create(queueName).catch(() => {});
    logWithTimestamp(`Mengirim ${workerMessages} pesan...`);

    const startTime = Date.now();

    // Send messages assigned to this worker
    for (let i = 0; i < workerMessages; i++) {
      const messageId = startMessageId + i;
      const sendTime = Date.now();
      const message = {
        id: messageId,
        sendTime: sendTime,
        data: `Test message ${messageId}`,
        payload: payload
      };

      try {
        await pgmq.msg.send(queueName, message);
        sent++;

        // Use string concatenation instead of array for better performance
        csvData += `${messageId},${messageSizeKB},${sendTime},${workerId}\n`;

        // Write buffer to file periodically - less frequent writes for better performance
        if (sent % bufferSize === 0) {
          // Non-blocking write operation
          setImmediate(async () => {
            await appendToCsv(csvFilePath, csvData);
          });
          csvData = ''; // Reset buffer
        }

        // Log progress periodically
        if (sent % 100 === 0 || sent === workerMessages) {
          const currentTime = Date.now();
          const rate = sent / ((currentTime - startTime) / 1000);
          logWithTimestamp(`${sent} pesan telah dikirim (${rate.toFixed(2)} pesan/detik)`);
        }
      } catch (err) {
        logWithTimestamp(`Error saat mengirim pesan ID-${messageId}: ${err.message}`);
      }
    }

    // Write any remaining data in buffer
    if (csvData.length > 0) {
      await appendToCsv(csvFilePath, csvData);
    }

    const endTime = Date.now();
    const throughput = sent / ((endTime - startTime) / 1000);
    logWithTimestamp(`Throughput Producer: ${throughput.toFixed(2)} pesan/detik`);
    logWithTimestamp('Producer selesai');
  } catch (error) {
    logWithTimestamp(`Error producer: ${error.message}`);
  } finally {
    if (pgmq) await pgmq.close();
  }
}

runProducer();
