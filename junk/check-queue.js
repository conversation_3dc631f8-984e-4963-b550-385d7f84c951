// Script to check PGMQ queue status and metrics
const { Pgmq } = require('pgmq-js');
const { Client } = require('pg');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433', 10),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: false,
};

async function checkQueue() {
  const queueName = process.env.QUEUE_NAME || 'pgmq_test';
  let pgmq;
  
  try {
    console.log('Connecting to PostgreSQL...');
    pgmq = await Pgmq.new(dbConfig);
    
    // Get queue metrics
    console.log('\n=== Queue Metrics ===');
    try {
      const metrics = await pgmq.queue.getMetrics(queueName);
      console.log(`Queue Name: ${metrics.queueName}`);
      console.log(`Queue Length: ${metrics.queueLength}`);
      console.log(`Total Messages: ${metrics.totalMessages}`);
      console.log(`Newest Message Age (sec): ${metrics.newestMsgAgeSec || 'N/A'}`);
      console.log(`Oldest Message Age (sec): ${metrics.oldestMsgAgeSec || 'N/A'}`);
      console.log(`Scrape Time: ${metrics.scrapeTime}`);
    } catch (error) {
      console.log(`Error getting metrics: ${error.message}`);
    }
    
    // List all queues
    console.log('\n=== All Queues ===');
    try {
      const queues = await pgmq.queue.list();
      queues.forEach(queue => {
        console.log(`- ${queue.name} (created: ${queue.createdAt}, partitioned: ${queue.isPartitioned})`);
      });
    } catch (error) {
      console.log(`Error listing queues: ${error.message}`);
    }
    
    // Try to read a message without consuming it (peek)
    console.log('\n=== Sample Message Check ===');
    try {
      const msg = await pgmq.msg.read(queueName, 1); // Very short visibility timeout
      if (msg) {
        console.log(`Found message with ID: ${msg.msgId}`);
        console.log(`Read count: ${msg.readCount}`);
        console.log(`Enqueued at: ${msg.enqueuedAt}`);
        console.log(`Visibility timeout: ${msg.vt}`);
        console.log(`Message content preview: ${JSON.stringify(msg.message).substring(0, 100)}...`);
        
        // Set the message back to be immediately available
        await pgmq.msg.setVt(queueName, msg.msgId, 0);
        console.log('Message visibility timeout reset to make it immediately available again');
      } else {
        console.log('No messages found in queue');
      }
    } catch (error) {
      console.log(`Error reading message: ${error.message}`);
    }
    
  } catch (error) {
    console.error(`Error: ${error.message}`);
  } finally {
    if (pgmq) await pgmq.close();
  }
}

checkQueue();
