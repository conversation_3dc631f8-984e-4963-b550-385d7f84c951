// Consumer for PGMQ, reads and processes messages from the queue for benchmarking
const { Pgmq } = require('pgmq-js');
const { Client } = require('pg');
const fs = require('fs').promises; // Use async fs operations
const fsSync = require('fs'); // Keep sync for initial setup only
const path = require('path');

function logWithTimestamp(message) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  const workerId = process.env.pm_id || process.pid;
  console.log(`[${timestamp}] [Worker-${workerId}] ${message}`);
}

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433', 10),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: false,
};

// Optimized CSV setup with minimal file system operations
function setupCsvFile() {
  const metricsDir = path.join(__dirname, 'metrics');

  // Use synchronous operations only for initial setup
  if (!fsSync.existsSync(metricsDir)) {
    fsSync.mkdirSync(metricsDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/:/g, '-').replace('T', '_').split('.')[0];

  // Simple header creation - let each worker create its own file to avoid locking
  const workerId = process.env.pm_id || process.pid;
  const workerFileName = `consumer_metrics_${timestamp}_worker_${workerId}.csv`;
  const workerFilePath = path.join(metricsDir, workerFileName);

  if (!fsSync.existsSync(workerFilePath)) {
    fsSync.writeFileSync(workerFilePath, 'message_id,size_kb,sent_timestamp,received_timestamp,latency_ms,worker_id\n');
  }

  return workerFilePath;
}

// High-performance async CSV writing with minimal locking overhead
async function appendToCsv(filePath, data) {
  try {
    // Use async file operations to avoid blocking the event loop
    await fs.appendFile(filePath, data);
  } catch (error) {
    // Simple fallback without complex retry logic
    logWithTimestamp(`CSV write error: ${error.message}`);
    // Try once more synchronously as fallback
    try {
      fsSync.appendFileSync(filePath, data);
    } catch (fallbackError) {
      logWithTimestamp(`CSV fallback write failed: ${fallbackError.message}`);
    }
  }
}

async function setupPgmq() {
  logWithTimestamp('Menyiapkan ekstensi PGMQ...');
  const client = new Client(dbConfig);
  try {
    await client.connect();
    await client.query('CREATE EXTENSION IF NOT EXISTS pgmq');
    await client.query('CREATE SCHEMA IF NOT EXISTS pgmq');
  } catch (error) {
    logWithTimestamp(`Error saat menyiapkan PGMQ: ${error.message}`);
    throw error;
  } finally {
    await client.end();
  }
}

async function runConsumer() {
  await setupPgmq();
  const queueName = process.env.QUEUE_NAME || 'pgmq_test';
  const expectedTotalMessages = parseInt(process.env.TOTAL_MESSAGES || '10000', 10);
  const workerId = process.env.pm_id || process.pid;

  // Setup CSV file (shared across all workers)
  const csvFilePath = setupCsvFile();
  logWithTimestamp(`Menyimpan data ke: ${csvFilePath}`);

  let processed = 0;
  let pgmq;
  const bufferSize = 500; // Increased buffer size for better performance (write every ~10-20 seconds)
  let consecutiveEmptyReads = 0;
  const maxEmptyReads = 30; // Stop after 30 consecutive empty reads (30 seconds)

  // Pre-allocate string builder for better performance
  let csvData = '';

  try {
    logWithTimestamp('Menghubungkan ke PostgreSQL...');
    pgmq = await Pgmq.new(dbConfig);
    let startTime = Date.now();

    logWithTimestamp(`Starting consumer worker. Expected total messages: ${expectedTotalMessages}`);

    // Process messages until queue is empty (not until a fixed count)
    while (consecutiveEmptyReads < maxEmptyReads) {
      try {
        const msg = await pgmq.msg.read(queueName, 30);
        if (msg) {
          consecutiveEmptyReads = 0; // Reset counter when we find a message
          const receiveTime = Date.now();
          const messageContent = msg.message;

          // Calculate message size from payload
          const messageSizeKB = messageContent.payload ?
            Math.round(messageContent.payload.length / 1024) : 0;

          // Calculate latency
          const latencyMs = receiveTime - messageContent.sendTime;

          // Use string concatenation instead of array for better performance
          csvData += `${messageContent.id},${messageSizeKB},${messageContent.sendTime},${receiveTime},${latencyMs},${workerId}\n`;

          await pgmq.msg.archive(queueName, msg.msgId);
          processed++;

          // Write buffer to file periodically - less frequent writes for better performance
          if (processed % bufferSize === 0) {
            // Non-blocking write operation
            setImmediate(async () => {
              await appendToCsv(csvFilePath, csvData);
            });
            csvData = ''; // Reset buffer
          }

          if (processed % 1000 === 0) {
            logWithTimestamp(`${processed} pesan telah diproses`);
          }
        } else {
          consecutiveEmptyReads++;
          if (consecutiveEmptyReads % 10 === 0) {
            logWithTimestamp(`No messages found for ${consecutiveEmptyReads} consecutive reads. Processed so far: ${processed}`);
          }
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait longer when queue is empty
        }
      } catch (error) {
        logWithTimestamp(`Error consumer: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Write any remaining data in buffer
    if (csvData.length > 0) {
      await appendToCsv(csvFilePath, csvData);
    }

    const endTime = Date.now();
    const throughput = processed / ((endTime - startTime) / 1000);
    logWithTimestamp(`Worker ${workerId} processed ${processed} messages`);
    logWithTimestamp(`Throughput Consumer: ${throughput.toFixed(2)} pesan/detik`);
    logWithTimestamp(`Consumer finished after ${consecutiveEmptyReads} consecutive empty reads`);
  } catch (error) {
    logWithTimestamp(`Error consumer: ${error.message}`);
  } finally {
    if (pgmq) await pgmq.close();
  }
}

runConsumer();
