#!/bin/bash

# PGMQ High-Performance Benchmark Script
# Optimized for multiple workers with maximum throughput

set -e

# Configuration - Optimized for 10K messages target
TOTAL_MESSAGES=${TOTAL_MESSAGES:-10000}
MESSAGE_SIZE_KB=${MESSAGE_SIZE_KB:-1}
QUEUE_NAME=${QUEUE_NAME:-pgmq_benchmark}
PRODUCER_WORKERS=${PRODUCER_WORKERS:-2}
CONSUMER_WORKERS=${CONSUMER_WORKERS:-4}

# Database configuration
export DB_HOST=${DB_HOST:-localhost}
export DB_PORT=${DB_PORT:-5433}
export DB_NAME=${DB_NAME:-postgres}
export DB_USER=${DB_USER:-postgres}
export DB_PASSWORD=${DB_PASSWORD:-postgres}

# Performance settings
export TOTAL_MESSAGES=$TOTAL_MESSAGES
export MESSAGE_SIZE_KB=$MESSAGE_SIZE_KB
export QUEUE_NAME=$QUEUE_NAME

echo "=== PGMQ High-Performance Benchmark ==="
echo "Total Messages: $TOTAL_MESSAGES"
echo "Message Size: ${MESSAGE_SIZE_KB}KB"
echo "Queue Name: $QUEUE_NAME"
echo "Producer Workers: $PRODUCER_WORKERS"
echo "Consumer Workers: $CONSUMER_WORKERS"
echo "======================================="

# Clean up any existing processes
cleanup() {
    echo "Cleaning up processes..."
    pkill -f "node producer.js" 2>/dev/null || true
    pkill -f "node consumer.js" 2>/dev/null || true
    sleep 2
}

# Cleanup on script exit
trap cleanup EXIT

# Clean metrics directory
echo "Cleaning previous metrics..."
rm -rf metrics/*.csv 2>/dev/null || true
mkdir -p metrics

# Start consumers first (they will wait for messages)
echo "Starting $CONSUMER_WORKERS consumer workers..."
for i in $(seq 0 $((CONSUMER_WORKERS-1))); do
    export pm_id="consumer_$i"
    nohup node consumer.js > "logs/consumer_$i.log" 2>&1 &
    echo "Started consumer worker $i (PID: $!)"
done

# Wait a moment for consumers to initialize
sleep 3

# Start producers
echo "Starting $PRODUCER_WORKERS producer workers..."
export instances=$PRODUCER_WORKERS

for i in $(seq 0 $((PRODUCER_WORKERS-1))); do
    export NODE_APP_INSTANCE=$i
    export pm_id="producer_$i"
    nohup node producer.js > "logs/producer_$i.log" 2>&1 &
    echo "Started producer worker $i (PID: $!)"
done

echo "All workers started. Monitoring progress..."

# Monitor producer completion
producer_pids=$(pgrep -f "node producer.js" || true)
if [ -n "$producer_pids" ]; then
    echo "Waiting for producers to complete..."
    while pgrep -f "node producer.js" > /dev/null; do
        sleep 5
        echo "Producers still running..."
    done
    echo "All producers completed!"
fi

# Wait additional time for consumers to process remaining messages
echo "Waiting for consumers to process remaining messages..."
sleep 10

# Stop consumers
echo "Stopping consumers..."
pkill -f "node consumer.js" 2>/dev/null || true

# Wait for graceful shutdown
sleep 3

echo "Benchmark completed! Aggregating results..."

# Aggregate results
node -e "
const fs = require('fs');
const path = require('path');

console.log('\\n=== BENCHMARK RESULTS ===');

// Aggregate producer metrics
const producerFiles = fs.readdirSync('metrics').filter(f => f.startsWith('producer_metrics_'));
let totalSent = 0;
let producerThroughput = 0;

producerFiles.forEach(file => {
    const content = fs.readFileSync(path.join('metrics', file), 'utf8');
    const lines = content.split('\\n').filter(line => line.trim() && !line.startsWith('message_id'));
    totalSent += lines.length;
});

// Aggregate consumer metrics
const consumerFiles = fs.readdirSync('metrics').filter(f => f.startsWith('consumer_metrics_'));
let totalReceived = 0;
let totalLatency = 0;
let minLatency = Infinity;
let maxLatency = 0;

consumerFiles.forEach(file => {
    const content = fs.readFileSync(path.join('metrics', file), 'utf8');
    const lines = content.split('\\n').filter(line => line.trim() && !line.startsWith('message_id'));

    lines.forEach(line => {
        const parts = line.split(',');
        if (parts.length >= 5) {
            const latency = parseInt(parts[4]);
            if (!isNaN(latency)) {
                totalReceived++;
                totalLatency += latency;
                minLatency = Math.min(minLatency, latency);
                maxLatency = Math.max(maxLatency, latency);
            }
        }
    });
});

const avgLatency = totalReceived > 0 ? totalLatency / totalReceived : 0;

console.log(\`Messages Sent: \${totalSent}\`);
console.log(\`Messages Received: \${totalReceived}\`);
console.log(\`Message Loss: \${totalSent - totalReceived} (\${((totalSent - totalReceived) / totalSent * 100).toFixed(2)}%)\`);
console.log(\`Average Latency: \${avgLatency.toFixed(2)}ms\`);
console.log(\`Min Latency: \${minLatency === Infinity ? 'N/A' : minLatency + 'ms'}\`);
console.log(\`Max Latency: \${maxLatency}ms\`);
console.log(\`Producer Workers: $PRODUCER_WORKERS\`);
console.log(\`Consumer Workers: $CONSUMER_WORKERS\`);
console.log('========================\\n');
"

echo "Results saved in metrics/ directory"
echo "Individual worker logs saved in logs/ directory"
echo "Benchmark completed successfully!"
