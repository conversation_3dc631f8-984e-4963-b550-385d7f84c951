[2025-05-31 04:44:47] [Worker-consumer_2] Menyiapkan ekstensi PGMQ...
[2025-05-31 04:44:47] [Worker-consumer_2] Menyimpan data ke: /home/<USER>/code/new kuliah/metrics/consumer_metrics_2025-05-31_04-44-47_worker_consumer_2.csv
[2025-05-31 04:44:47] [Worker-consumer_2] Menghubungkan ke PostgreSQL...
[2025-05-31 04:44:47] [Worker-consumer_2] Starting consumer worker. Expected total messages: 100000
[2025-05-31 04:44:47] [Worker-consumer_2] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:48] [Worker-consumer_2] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:48] [Worker-consumer_2] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:49] [Worker-consumer_2] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:49] [Worker-consumer_2] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:50] [Worker-consumer_2] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:57] [Worker-consumer_2] 1000 pesan telah diproses
[2025-05-31 04:45:03] [Worker-consumer_2] 2000 pesan telah diproses
[2025-05-31 04:45:07] [Worker-consumer_2] 3000 pesan telah diproses
[2025-05-31 04:45:10] [Worker-consumer_2] 4000 pesan telah diproses
[2025-05-31 04:45:19] [Worker-consumer_2] 5000 pesan telah diproses
[2025-05-31 04:45:36] [Worker-consumer_2] 6000 pesan telah diproses
[2025-05-31 04:45:49] [Worker-consumer_2] 7000 pesan telah diproses
[2025-05-31 04:46:07] [Worker-consumer_2] 8000 pesan telah diproses
