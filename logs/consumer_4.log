[2025-05-31 04:44:47] [Worker-consumer_4] Menyiapkan ekstensi PGMQ...
[2025-05-31 04:44:47] [Worker-consumer_4] Menyimpan data ke: /home/<USER>/code/new kuliah/metrics/consumer_metrics_2025-05-31_04-44-47_worker_consumer_4.csv
[2025-05-31 04:44:47] [Worker-consumer_4] Menghubungkan ke PostgreSQL...
[2025-05-31 04:44:47] [Worker-consumer_4] Starting consumer worker. Expected total messages: 100000
[2025-05-31 04:44:47] [Worker-consumer_4] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:48] [Worker-consumer_4] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:48] [Worker-consumer_4] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:49] [Worker-consumer_4] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:49] [Worker-consumer_4] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:50] [Worker-consumer_4] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:50] [Worker-consumer_4] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:58] [Worker-consumer_4] 1000 pesan telah diproses
[2025-05-31 04:45:03] [Worker-consumer_4] 2000 pesan telah diproses
[2025-05-31 04:45:07] [Worker-consumer_4] 3000 pesan telah diproses
[2025-05-31 04:45:11] [Worker-consumer_4] 4000 pesan telah diproses
[2025-05-31 04:45:21] [Worker-consumer_4] 5000 pesan telah diproses
[2025-05-31 04:45:37] [Worker-consumer_4] 6000 pesan telah diproses
[2025-05-31 04:45:51] [Worker-consumer_4] 7000 pesan telah diproses
[2025-05-31 04:46:09] [Worker-consumer_4] 8000 pesan telah diproses
