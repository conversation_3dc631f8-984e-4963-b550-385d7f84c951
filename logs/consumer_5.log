[2025-05-31 04:44:47] [Worker-consumer_5] Menyiapkan ekstensi PGMQ...
[2025-05-31 04:44:47] [Worker-consumer_5] Menyimpan data ke: /home/<USER>/code/new kuliah/metrics/consumer_metrics_2025-05-31_04-44-47_worker_consumer_5.csv
[2025-05-31 04:44:47] [Worker-consumer_5] Menghubungkan ke PostgreSQL...
[2025-05-31 04:44:47] [Worker-consumer_5] Starting consumer worker. Expected total messages: 100000
[2025-05-31 04:44:47] [Worker-consumer_5] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:48] [Worker-consumer_5] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:48] [Worker-consumer_5] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:49] [Worker-consumer_5] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:49] [Worker-consumer_5] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:50] [Worker-consumer_5] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:57] [Worker-consumer_5] 1000 pesan telah diproses
[2025-05-31 04:45:03] [Worker-consumer_5] 2000 pesan telah diproses
[2025-05-31 04:45:06] [Worker-consumer_5] 3000 pesan telah diproses
[2025-05-31 04:45:10] [Worker-consumer_5] 4000 pesan telah diproses
[2025-05-31 04:45:19] [Worker-consumer_5] 5000 pesan telah diproses
[2025-05-31 04:45:35] [Worker-consumer_5] 6000 pesan telah diproses
[2025-05-31 04:45:49] [Worker-consumer_5] 7000 pesan telah diproses
[2025-05-31 04:46:07] [Worker-consumer_5] 8000 pesan telah diproses
