[2025-05-31 04:44:47] [Worker-consumer_6] Menyiapkan ekstensi PGMQ...
[2025-05-31 04:44:47] [Worker-consumer_6] Menyimpan data ke: /home/<USER>/code/new kuliah/metrics/consumer_metrics_2025-05-31_04-44-47_worker_consumer_6.csv
[2025-05-31 04:44:47] [Worker-consumer_6] Menghubungkan ke PostgreSQL...
[2025-05-31 04:44:47] [Worker-consumer_6] Starting consumer worker. Expected total messages: 100000
[2025-05-31 04:44:47] [Worker-consumer_6] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:48] [Worker-consumer_6] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:48] [Worker-consumer_6] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:49] [Worker-consumer_6] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:49] [Worker-consumer_6] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:50] [Worker-consumer_6] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:50] [Worker-consumer_6] Error consumer: relation "pgmq.q_pgmq_benchmark" does not exist
[2025-05-31 04:44:58] [Worker-consumer_6] 1000 pesan telah diproses
[2025-05-31 04:45:03] [Worker-consumer_6] 2000 pesan telah diproses
[2025-05-31 04:45:07] [Worker-consumer_6] 3000 pesan telah diproses
[2025-05-31 04:45:10] [Worker-consumer_6] 4000 pesan telah diproses
[2025-05-31 04:45:20] [Worker-consumer_6] 5000 pesan telah diproses
[2025-05-31 04:45:37] [Worker-consumer_6] 6000 pesan telah diproses
[2025-05-31 04:45:51] [Worker-consumer_6] 7000 pesan telah diproses
[2025-05-31 04:46:08] [Worker-consumer_6] 8000 pesan telah diproses
